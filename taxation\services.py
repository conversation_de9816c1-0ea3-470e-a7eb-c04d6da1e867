"""
Tax calculation services for GST and other taxes
"""
from decimal import Decimal, ROUND_HALF_UP
from django.utils import timezone
from .models import TaxCategory, GSTRate, TaxConfiguration, TaxCalculation


class TaxCalculationService:
    """
    Service class for calculating taxes on cart/order amounts
    """
    
    @staticmethod
    def get_active_tax_configuration():
        """Get the currently active tax configuration"""
        try:
            return TaxConfiguration.objects.filter(is_active=True).first()
        except TaxConfiguration.DoesNotExist:
            return None
    
    @staticmethod
    def get_effective_gst_rates(tax_category=None):
        """Get currently effective GST rates for a tax category"""
        if not tax_category:
            config = TaxCalculationService.get_active_tax_configuration()
            if config and config.default_tax_category:
                tax_category = config.default_tax_category
            else:
                return {}
        
        rates = {}
        gst_rates = GSTRate.objects.filter(
            tax_category=tax_category,
            is_active=True
        )
        
        for rate in gst_rates:
            if rate.is_currently_effective():
                rates[rate.gst_type] = rate.rate_percentage
        
        return rates
    
    @staticmethod
    def calculate_gst(amount, tax_category=None):
        """
        Calculate GST amounts for given amount and tax category
        Returns dict with CGST, SGST, IGST, UGST amounts
        """
        if amount <= 0:
            return {
                'cgst': Decimal('0.00'),
                'sgst': Decimal('0.00'),
                'igst': Decimal('0.00'),
                'ugst': Decimal('0.00'),
                'total_gst': Decimal('0.00')
            }
        
        # Get tax configuration
        config = TaxCalculationService.get_active_tax_configuration()
        if not config:
            return {
                'cgst': Decimal('0.00'),
                'sgst': Decimal('0.00'),
                'igst': Decimal('0.00'),
                'ugst': Decimal('0.00'),
                'total_gst': Decimal('0.00')
            }
        
        # Check tax exemption threshold
        if amount < config.tax_exemption_threshold:
            return {
                'cgst': Decimal('0.00'),
                'sgst': Decimal('0.00'),
                'igst': Decimal('0.00'),
                'ugst': Decimal('0.00'),
                'total_gst': Decimal('0.00')
            }
        
        # Get GST rates
        gst_rates = TaxCalculationService.get_effective_gst_rates(tax_category)
        
        # Calculate individual GST components
        # Note: Apply either CGST+SGST (intra-state) OR IGST (inter-state), not both
        cgst = Decimal('0.00')
        sgst = Decimal('0.00')
        igst = Decimal('0.00')
        ugst = Decimal('0.00')

        # For now, default to intra-state (CGST + SGST)
        # In a real system, this would be determined by customer and provider locations
        if 'CGST' in gst_rates and 'SGST' in gst_rates:
            # Intra-state transaction: Apply CGST + SGST
            cgst = (amount * gst_rates['CGST']) / Decimal('100')
            sgst = (amount * gst_rates['SGST']) / Decimal('100')
        elif 'IGST' in gst_rates:
            # Inter-state transaction: Apply IGST only
            igst = (amount * gst_rates['IGST']) / Decimal('100')
        elif 'UGST' in gst_rates:
            # Union Territory transaction: Apply UGST only
            ugst = (amount * gst_rates['UGST']) / Decimal('100')
        
        # Round to nearest paisa if configured
        if config.round_tax_to_nearest_paisa:
            cgst = cgst.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            sgst = sgst.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            igst = igst.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            ugst = ugst.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        total_gst = cgst + sgst + igst + ugst
        
        return {
            'cgst': cgst,
            'sgst': sgst,
            'igst': igst,
            'ugst': ugst,
            'total_gst': total_gst
        }
    
    @staticmethod
    def calculate_service_charge(amount):
        """Calculate platform service charge"""
        config = TaxCalculationService.get_active_tax_configuration()
        if not config or config.service_charge_percentage <= 0:
            return Decimal('0.00')
        
        service_charge = (amount * config.service_charge_percentage) / Decimal('100')
        
        if config.round_tax_to_nearest_paisa:
            service_charge = service_charge.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        
        return service_charge
    
    @staticmethod
    def calculate_total_tax_and_charges(subtotal, tax_category=None):
        """
        Calculate complete tax breakdown for a given subtotal
        Returns comprehensive tax calculation
        """
        if subtotal <= 0:
            return {
                'subtotal': Decimal('0.00'),
                'cgst': Decimal('0.00'),
                'sgst': Decimal('0.00'),
                'igst': Decimal('0.00'),
                'ugst': Decimal('0.00'),
                'total_gst': Decimal('0.00'),
                'service_charge': Decimal('0.00'),
                'total_tax_and_charges': Decimal('0.00'),
                'grand_total': Decimal('0.00'),
                'tax_category': tax_category.name if tax_category else 'Default',
                'calculation_details': {}
            }
        
        # Calculate GST
        gst_breakdown = TaxCalculationService.calculate_gst(subtotal, tax_category)
        
        # Calculate service charge
        service_charge = TaxCalculationService.calculate_service_charge(subtotal)
        
        # Calculate totals
        total_tax_and_charges = gst_breakdown['total_gst'] + service_charge
        grand_total = subtotal + total_tax_and_charges
        
        # Get configuration for details
        config = TaxCalculationService.get_active_tax_configuration()
        gst_rates = TaxCalculationService.get_effective_gst_rates(tax_category)
        
        return {
            'subtotal': subtotal,
            'cgst': gst_breakdown['cgst'],
            'sgst': gst_breakdown['sgst'],
            'igst': gst_breakdown['igst'],
            'ugst': gst_breakdown['ugst'],
            'total_gst': gst_breakdown['total_gst'],
            'service_charge': service_charge,
            'total_tax_and_charges': total_tax_and_charges,
            'grand_total': grand_total,
            'tax_category': tax_category.name if tax_category else 'Default',
            'calculation_details': {
                'gst_rates_applied': gst_rates,
                'service_charge_rate': config.service_charge_percentage if config else 0,
                'tax_exemption_threshold': config.tax_exemption_threshold if config else 0,
                'calculated_at': timezone.now().isoformat()
            }
        }
    
    @staticmethod
    def save_tax_calculation(reference_type, reference_id, tax_calculation):
        """
        Save tax calculation to database for audit purposes
        """
        try:
            config = TaxCalculationService.get_active_tax_configuration()
            
            tax_calc = TaxCalculation.objects.create(
                reference_type=reference_type,
                reference_id=reference_id,
                subtotal=tax_calculation['subtotal'],
                cgst_amount=tax_calculation['cgst'],
                sgst_amount=tax_calculation['sgst'],
                igst_amount=tax_calculation['igst'],
                ugst_amount=tax_calculation['ugst'],
                total_tax=tax_calculation['total_gst'],
                service_charge=tax_calculation['service_charge'],
                total_amount=tax_calculation['grand_total'],
                tax_configuration=config,
                calculation_details=tax_calculation['calculation_details']
            )
            
            return tax_calc
        except Exception as e:
            # Log error but don't fail the main operation
            print(f"Error saving tax calculation: {e}")
            return None


class TaxCategoryService:
    """
    Service for managing tax categories
    """
    
    @staticmethod
    def get_default_tax_category():
        """Get the default tax category"""
        config = TaxCalculationService.get_active_tax_configuration()
        if config and config.default_tax_category:
            return config.default_tax_category
        
        # Fallback to first active category
        return TaxCategory.objects.filter(is_active=True).first()
    
    @staticmethod
    def get_tax_category_for_service(service_id):
        """
        Get tax category for a specific service
        This would need to be implemented based on service categorization
        For now, returns default category
        """
        return TaxCategoryService.get_default_tax_category()
