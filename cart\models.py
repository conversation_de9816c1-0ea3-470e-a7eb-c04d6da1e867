from django.db import models
from django.conf import settings
from decimal import Decimal
from django.core.exceptions import ValidationError


class Cart(models.Model):
    """
    Shopping cart model supporting both authenticated and anonymous users.
    Stores cart state persistently for abandoned cart recovery.
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='+',  # Disable reverse relationship to prevent cross-database queries
        db_constraint=False  # Cross-database foreign key
    )
    session_key = models.CharField(
        max_length=40,
        null=True,
        blank=True,
        unique=True,
        help_text="Session key for anonymous users"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    # Calculated totals
    sub_total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    minimum_order_fee_applied = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    coupon_code_applied = models.CharField(
        max_length=50,
        blank=True,
        null=True
    )

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(user__isnull=False) | models.Q(session_key__isnull=False),
                name='cart_must_have_user_or_session'
            )
        ]
        ordering = ['-updated_at']

    def clean(self):
        if not self.user and not self.session_key:
            raise ValidationError("Cart must have either a user or session key.")

    def get_items_count(self):
        """Get total number of items in cart"""
        return self.items.aggregate(
            total=models.Sum('quantity')
        )['total'] or 0

    def get_unique_services_count(self):
        """Get count of unique services in cart"""
        return self.items.count()

    def calculate_subtotal(self):
        """Calculate subtotal from cart items"""
        subtotal = Decimal('0.00')
        for item in self.items.all():
            subtotal += item.get_total_price()
        return subtotal

    def update_totals(self, save=True):
        """Update all calculated totals including tax"""
        self.sub_total = self.calculate_subtotal()

        # Calculate tax using taxation service
        try:
            from taxation.services import TaxCalculationService
            tax_calculation = TaxCalculationService.calculate_total_tax_and_charges(self.sub_total)

            self.tax_amount = tax_calculation['total_gst']
            self.total_amount = tax_calculation['grand_total']

            # Save tax calculation for audit (only if cart has an ID)
            if self.id:
                TaxCalculationService.save_tax_calculation('cart', self.id, tax_calculation)

        except ImportError:
            # Fallback if taxation service is not available
            self.tax_amount = Decimal('0.00')
            self.total_amount = self.sub_total
        except Exception as e:
            # Log error but don't fail the cart operation
            print(f"Error calculating tax: {e}")
            self.tax_amount = Decimal('0.00')
            self.total_amount = self.sub_total

        # Only save if explicitly requested (prevent recursive calls)
        if save:
            self.save(update_fields=['sub_total', 'tax_amount', 'total_amount', 'updated_at'])

    def __str__(self):
        if self.user_id:
            return f"Cart {self.id} for User ID {self.user_id}"
        return f"Cart {self.id} for session {self.session_key}"


class CartItem(models.Model):
    """
    Individual items in a shopping cart.
    Stores price at time of addition for historical accuracy.
    """
    cart = models.ForeignKey(
        Cart,
        on_delete=models.CASCADE,
        related_name='items'
    )
    # Store service ID instead of foreign key to avoid cross-database issues
    service_id = models.PositiveIntegerField(
        help_text="ID of the service from catalogue database"
    )
    # Store service details at time of addition for historical accuracy
    service_title = models.CharField(max_length=255)
    service_image_url = models.URLField(blank=True, null=True)
    quantity = models.PositiveIntegerField(default=1)
    price_at_add = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Price of service when added to cart"
    )
    discount_at_add = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Discount applied when added to cart"
    )
    added_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('cart', 'service_id')
        ordering = ['added_at']

    def get_total_price(self):
        """Calculate total price for this cart item"""
        return (self.price_at_add - self.discount_at_add) * self.quantity

    def get_savings(self):
        """Calculate savings from discount"""
        return self.discount_at_add * self.quantity

    def save(self, *args, **kwargs):
        # Update cart totals when cart item is saved
        super().save(*args, **kwargs)
        # Update cart totals but don't save again to prevent recursion
        self.cart.update_totals(save=True)

    def delete(self, *args, **kwargs):
        cart = self.cart
        super().delete(*args, **kwargs)
        cart.update_totals(save=True)

    def __str__(self):
        return f"{self.quantity} x {self.service_title} in Cart {self.cart.id}"
