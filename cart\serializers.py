from rest_framework import serializers
from decimal import Decimal
from catalogue.models import Service
from .models import Cart, CartItem


class CartItemSerializer(serializers.ModelSerializer):
    """
    Serializer for CartItem with service details and calculations.
    """
    # Service details are now stored directly in the cart item
    total_price = serializers.ReadOnlyField(source='get_total_price')
    savings = serializers.ReadOnlyField(source='get_savings')

    class Meta:
        model = CartItem
        fields = [
            'id', 'service_id', 'service_title', 'service_image_url',
            'quantity', 'price_at_add', 'discount_at_add',
            'total_price', 'savings', 'added_at', 'updated_at'
        ]
        read_only_fields = ['service_title', 'service_image_url', 'price_at_add', 'discount_at_add', 'added_at', 'updated_at']


class CartSerializer(serializers.ModelSerializer):
    """
    Serializer for Cart with items and totals.
    """
    items = CartItemSerializer(many=True, read_only=True)
    items_count = serializers.ReadOnlyField(source='get_items_count')
    unique_services_count = serializers.ReadOnlyField(source='get_unique_services_count')

    class Meta:
        model = Cart
        fields = [
            'id', 'user', 'session_key', 'created_at', 'updated_at',
            'is_active', 'sub_total', 'tax_amount', 'discount_amount',
            'minimum_order_fee_applied', 'total_amount', 'coupon_code_applied',
            'items', 'items_count', 'unique_services_count'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'sub_total', 'tax_amount',
            'discount_amount', 'minimum_order_fee_applied', 'total_amount'
        ]


class AddToCartSerializer(serializers.Serializer):
    """
    Serializer for adding items to cart with validation.
    """
    service_id = serializers.IntegerField()
    quantity = serializers.IntegerField(min_value=1, default=1)

    def validate_service_id(self, value):
        """Validate that service exists and is active"""
        try:
            service = Service.objects.get(id=value, is_active=True)
            return value
        except Service.DoesNotExist:
            raise serializers.ValidationError("Service not found or inactive.")

    def validate(self, data):
        """
        Validate that all services in cart belong to same top-level category.
        This is a key business rule from the requirements.
        """
        service_id = data['service_id']

        try:
            service = Service.objects.get(id=service_id)
        except Service.DoesNotExist:
            raise serializers.ValidationError("Service not found.")

        # Get the cart from context
        cart = self.context.get('cart')
        if not cart:
            return data

        # Get the top-level category of the new service
        new_service_root_category = service.category.get_root()

        # Check existing cart items
        existing_items = cart.items.all()
        if existing_items.exists():
            # Get the first existing item's service ID and fetch the service
            first_item_service_id = existing_items.first().service_id
            try:
                first_item_service = Service.objects.get(id=first_item_service_id)
                first_item_root_category = first_item_service.category.get_root()

                # Compare top-level categories
                if new_service_root_category != first_item_root_category:
                    raise serializers.ValidationError(
                        f"All services in the cart must belong to the same top-level category. "
                        f"Current cart contains services from '{first_item_root_category.name}' "
                        f"but you're trying to add a service from '{new_service_root_category.name}'."
                    )
            except Service.DoesNotExist:
                # If existing service doesn't exist, allow the addition but log it
                pass

        return data


class UpdateCartItemSerializer(serializers.Serializer):
    """
    Serializer for updating cart item quantity.
    """
    quantity = serializers.IntegerField(min_value=1)


class ApplyCouponSerializer(serializers.Serializer):
    """
    Serializer for applying coupon codes to cart.
    """
    coupon_code = serializers.CharField(max_length=50)

    def validate_coupon_code(self, value):
        """Validate coupon code format"""
        if not value.strip():
            raise serializers.ValidationError("Coupon code cannot be empty.")
        return value.strip().upper()


class CartSummarySerializer(serializers.ModelSerializer):
    """
    Simplified cart serializer for summary views.
    """
    items_count = serializers.ReadOnlyField(source='get_items_count')
    unique_services_count = serializers.ReadOnlyField(source='get_unique_services_count')

    class Meta:
        model = Cart
        fields = [
            'id', 'sub_total', 'tax_amount', 'discount_amount',
            'minimum_order_fee_applied', 'total_amount', 'coupon_code_applied',
            'items_count', 'unique_services_count', 'updated_at'
        ]


class CartItemCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating cart items with automatic price capture.
    """
    class Meta:
        model = CartItem
        fields = ['service_id', 'quantity']

    def create(self, validated_data):
        """
        Create cart item with current service price and discount.
        """
        service_id = validated_data['service_id']
        cart = validated_data['cart']
        quantity = validated_data['quantity']

        # Get service details from catalogue database
        try:
            service = Service.objects.get(id=service_id, is_active=True)
        except Service.DoesNotExist:
            raise serializers.ValidationError("Service not found or inactive.")

        # Get current price and any applicable discount
        current_price = service.get_current_price()
        discount = service.base_price - current_price if service.discount_price else Decimal('0.00')

        # Check if item already exists in cart
        existing_item = CartItem.objects.filter(cart=cart, service_id=service_id).first()

        if existing_item:
            # Update quantity of existing item
            existing_item.quantity += quantity
            existing_item.save()
            return existing_item
        else:
            # Create new cart item with service details
            cart_item = CartItem.objects.create(
                cart=cart,
                service_id=service_id,
                service_title=service.title,
                service_image_url=service.image.url if service.image else None,
                quantity=quantity,
                price_at_add=service.base_price,
                discount_at_add=discount
            )
            return cart_item


class CheckoutValidationSerializer(serializers.Serializer):
    """
    Serializer for validating cart before checkout.
    """
    address_id = serializers.IntegerField(required=False)
    payment_method = serializers.ChoiceField(
        choices=[('razorpay', 'Razorpay'), ('cod', 'Cash on Delivery')],
        default='razorpay'
    )
    
    def validate(self, data):
        """Validate cart is ready for checkout"""
        cart = self.context.get('cart')
        
        if not cart or not cart.items.exists():
            raise serializers.ValidationError("Cart is empty.")
        
        if cart.total_amount <= 0:
            raise serializers.ValidationError("Cart total must be greater than zero.")
        
        return data
