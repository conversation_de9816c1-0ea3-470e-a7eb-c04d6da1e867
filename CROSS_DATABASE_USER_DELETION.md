# Cross-Database User Deletion Solution

## Problem
When trying to delete users from Django admin, you encountered the error:
```
relation "cart_cart" does not exist
```

This happens because your Django project uses a **multi-database microservices architecture** where:
- **User model** is in the `default` database (`home_services_auth`)
- **Cart, Orders, Payments** models are in separate databases but have foreign key relationships to User
- Django admin tries to check for related objects but looks in the wrong database

## Solution Implemented

### 1. Enhanced UserAdmin Class
Updated `authentication/admin.py` with custom delete methods that handle cross-database relationships:

**Key Features:**
- `delete_model()` - Handles single user deletion
- `delete_queryset()` - Handles bulk user deletion  
- Helper methods for each microservice database:
  - `_delete_user_cart_data()`
  - `_delete_user_order_data()`
  - `_delete_user_payment_data()`

**How it works:**
1. Before deleting a user, it checks all microservice databases
2. Deletes related records from cart, orders, and payments databases
3. Only then deletes the user from the authentication database
4. Includes error handling to prevent deletion failures

### 2. Management Command for Cleanup
Created `authentication/management/commands/cleanup_orphaned_data.py`:

**Usage:**
```bash
# Dry run to see what would be deleted
python manage.py cleanup_orphaned_data --dry-run

# Clean up all databases
python manage.py cleanup_orphaned_data

# Clean up specific database
python manage.py cleanup_orphaned_data --database=cart
python manage.py cleanup_orphaned_data --database=orders
python manage.py cleanup_orphaned_data --database=payments
```

**Features:**
- Finds orphaned records across all microservice databases
- Safe dry-run mode to preview changes
- Selective cleanup by database
- Comprehensive error handling

### 3. Database Relationships Handled

**Cart Database (`cart_db`):**
- `cart_cart.user_id` → `auth_user.id`
- `cart_cartitem` (deleted via cart relationship)

**Orders Database (`orders_db`):**
- `orders_order.customer_id` → `auth_user.id`
- `orders_order.assigned_provider_id` → `auth_user.id`
- `orders_orderitem` (deleted via order relationship)

**Payments Database (`payments_db`):**
- `payments_paymenttransaction.user_id` → `auth_user.id`

## Usage Instructions

### Deleting Users via Django Admin
1. Go to Django Admin → Authentication → Users
2. Select user(s) to delete
3. Choose "Delete selected users" action
4. The system will automatically:
   - Delete related cart data from `cart_db`
   - Delete related order data from `orders_db`  
   - Delete related payment data from `payments_db`
   - Delete the user from `default` database

### Maintenance Cleanup
Run the cleanup command periodically to remove orphaned data:
```bash
# Monthly cleanup (recommended)
python manage.py cleanup_orphaned_data
```

## Technical Details

### Database Constraint Handling
All cross-database foreign keys use `db_constraint=False` to prevent PostgreSQL foreign key constraint errors while allowing Django ORM relationships.

### Error Handling
- Each database operation is wrapped in try-catch blocks
- Errors are logged but don't prevent user deletion
- Admin interface shows success/error messages

### Performance Considerations
- Uses direct SQL queries for efficiency
- Checks table existence before operations
- Batches operations where possible

## Future Enhancements

1. **Async Processing**: For large datasets, consider using Celery for background deletion
2. **Audit Trail**: Log all deletions for compliance
3. **Soft Deletes**: Consider implementing soft deletes instead of hard deletes
4. **API Integration**: Add API endpoints for programmatic user deletion

## Troubleshooting

### If deletion still fails:
1. Check database connections in `settings.py`
2. Verify all migrations are applied: `python manage.py showmigrations`
3. Run cleanup command first: `python manage.py cleanup_orphaned_data --dry-run`
4. Check Django logs for specific error messages

### Common Issues:
- **Table doesn't exist**: Run migrations for the specific database
- **Permission denied**: Check database user permissions
- **Connection timeout**: Increase database timeout settings

## Security Notes
- Only staff users can access the admin interface
- All operations are logged in Django admin logs
- Database operations use parameterized queries to prevent SQL injection
- Dry-run mode available for safe testing
