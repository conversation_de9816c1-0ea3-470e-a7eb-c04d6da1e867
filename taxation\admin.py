from django.contrib import admin
from django.utils.html import format_html
from .models import TaxCategory, GSTRate, TaxConfiguration, TaxCalculation


@admin.register(TaxCategory)
class TaxCategoryAdmin(admin.ModelAdmin):
    """
    Admin interface for Tax Categories
    """
    list_display = ['name', 'description', 'is_active', 'get_gst_rates_count', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_gst_rates_count(self, obj):
        count = obj.gst_rates.filter(is_active=True).count()
        return format_html(
            '<span style="color: {};">{} active rates</span>',
            'green' if count > 0 else 'red',
            count
        )
    get_gst_rates_count.short_description = 'Active GST Rates'


class GSTRateInline(admin.TabularInline):
    """
    Inline admin for GST Rates
    """
    model = GSTRate
    extra = 1
    fields = ['gst_type', 'rate_percentage', 'hsn_code', 'effective_from', 'effective_until', 'is_active']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(GSTRate)
class GSTRateAdmin(admin.ModelAdmin):
    """
    Admin interface for GST Rates
    """
    list_display = [
        'tax_category', 'gst_type', 'rate_percentage', 'hsn_code',
        'effective_from', 'effective_until', 'is_currently_effective', 'is_active'
    ]
    list_filter = ['gst_type', 'is_active', 'effective_from', 'tax_category']
    search_fields = ['tax_category__name', 'hsn_code']
    readonly_fields = ['created_at', 'updated_at', 'is_currently_effective']
    date_hierarchy = 'effective_from'

    fieldsets = (
        (None, {
            'fields': ('tax_category', 'gst_type', 'rate_percentage', 'hsn_code')
        }),
        ('Validity Period', {
            'fields': ('effective_from', 'effective_until', 'is_active', 'is_currently_effective')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def is_currently_effective(self, obj):
        is_effective = obj.is_currently_effective()
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            'green' if is_effective else 'red',
            'Yes' if is_effective else 'No'
        )
    is_currently_effective.short_description = 'Currently Effective'
    is_currently_effective.boolean = True


@admin.register(TaxConfiguration)
class TaxConfigurationAdmin(admin.ModelAdmin):
    """
    Admin interface for Tax Configuration
    """
    list_display = [
        'name', 'tax_exemption_threshold', 'default_tax_category',
        'service_charge_percentage', 'is_active', 'updated_at'
    ]
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Tax Settings', {
            'fields': (
                'tax_exemption_threshold', 'default_tax_category',
                'round_tax_to_nearest_paisa'
            )
        }),
        ('Service Charges', {
            'fields': ('service_charge_percentage',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        # Ensure only one active configuration
        if obj.is_active:
            TaxConfiguration.objects.filter(is_active=True).exclude(pk=obj.pk).update(is_active=False)
        super().save_model(request, obj, form, change)


@admin.register(TaxCalculation)
class TaxCalculationAdmin(admin.ModelAdmin):
    """
    Admin interface for Tax Calculations (Read-only for audit)
    """
    list_display = [
        'reference_type', 'reference_id', 'subtotal', 'total_tax',
        'service_charge', 'total_amount', 'calculated_at'
    ]
    list_filter = ['reference_type', 'calculated_at']
    search_fields = ['reference_id']
    readonly_fields = [
        'reference_type', 'reference_id', 'subtotal', 'cgst_amount',
        'sgst_amount', 'igst_amount', 'ugst_amount', 'total_tax',
        'service_charge', 'total_amount', 'tax_configuration',
        'calculated_at', 'calculation_details'
    ]

    fieldsets = (
        ('Reference', {
            'fields': ('reference_type', 'reference_id')
        }),
        ('Tax Breakdown', {
            'fields': (
                'subtotal', 'cgst_amount', 'sgst_amount',
                'igst_amount', 'ugst_amount', 'total_tax'
            )
        }),
        ('Final Amounts', {
            'fields': ('service_charge', 'total_amount')
        }),
        ('Configuration', {
            'fields': ('tax_configuration', 'calculated_at')
        }),
        ('Details', {
            'fields': ('calculation_details',),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        """Disable manual addition of tax calculations"""
        return False

    def has_change_permission(self, request, obj=None):
        """Make tax calculations read-only"""
        return False

    def has_delete_permission(self, request, obj=None):
        """Allow deletion for cleanup"""
        return request.user.is_superuser

    actions = ['export_tax_calculations']

    def export_tax_calculations(self, request, queryset):
        """Export selected tax calculations"""
        # This could be implemented to export to CSV/Excel
        self.message_user(request, f"Export functionality for {queryset.count()} calculations")
    export_tax_calculations.short_description = "Export selected tax calculations"
