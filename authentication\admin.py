from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db import connections
from django.core.exceptions import FieldError
from .models import User, Address, FailedLoginAttempt


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Enhanced admin interface for User model
    """
    list_display = [
        'name', 'email', 'mobile_number', 'user_type',
        'is_verified', 'is_active', 'account_status', 'date_joined'
    ]
    list_filter = [
        'user_type', 'is_verified', 'is_active', 'is_locked',
        'date_joined', 'last_login'
    ]
    search_fields = ['name', 'email', 'mobile_number']
    ordering = ['-date_joined']

    fieldsets = (
        (None, {
            'fields': ('email', 'mobile_number', 'password')
        }),
        ('Personal Info', {
            'fields': ('name', 'profile_picture')
        }),
        ('User Type & Status', {
            'fields': ('user_type', 'is_verified', 'is_active')
        }),
        ('Security', {
            'fields': ('is_locked', 'lockout_until')
        }),
        ('Permissions', {
            'fields': ('is_staff', 'is_superuser', 'groups', 'user_permissions'),
            'classes': ('collapse',)
        }),
        ('Important Dates', {
            'fields': ('last_login', 'date_joined'),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'mobile_number', 'name', 'user_type', 'password1', 'password2'),
        }),
    )

    readonly_fields = ['date_joined', 'last_login']

    def account_status(self, obj):
        """Display account status with color coding"""
        if obj.is_locked:
            if obj.lockout_until and timezone.now() > obj.lockout_until:
                return format_html(
                    '<span style="color: orange;">🔓 Lockout Expired</span>'
                )
            return format_html(
                '<span style="color: red;">🔒 Locked</span>'
            )
        elif not obj.is_verified:
            return format_html(
                '<span style="color: orange;">⚠️ Unverified</span>'
            )
        elif obj.is_active:
            return format_html(
                '<span style="color: green;">✅ Active</span>'
            )
        else:
            return format_html(
                '<span style="color: gray;">❌ Inactive</span>'
            )

    account_status.short_description = 'Status'

    def get_queryset(self, request):
        """
        Override queryset to avoid cross-database relationship issues.
        Only select_related for same-database relationships.
        """
        # Only select related objects that are in the same database (default)
        # Avoid any cross-database relationships like carts, orders, payments
        return super().get_queryset(request)

    def get_deleted_objects(self, objs, request):
        """
        Override to prevent Django from checking cross-database relationships
        when determining what objects would be deleted.
        """
        try:
            # Use the parent method but catch any cross-database errors
            return super().get_deleted_objects(objs, request)
        except Exception as e:
            # If there's a cross-database error, return a simplified response
            if 'does not exist' in str(e) and 'cart' in str(e).lower():
                # Return a simplified deleted objects structure
                if hasattr(objs, '__iter__') and not isinstance(objs, str):
                    obj_count = len(list(objs))
                else:
                    obj_count = 1

                return (
                    [f"{obj_count} user(s)"],  # deletable_objects
                    {User._meta.verbose_name_plural: obj_count},  # model_count
                    set(),  # perms_needed
                    set()   # protected
                )

    def changelist_view(self, request, extra_context=None):
        """
        Override changelist view to handle cross-database relationship errors
        """
        try:
            return super().changelist_view(request, extra_context)
        except Exception as e:
            if 'does not exist' in str(e) and ('cart' in str(e).lower() or 'order' in str(e).lower() or 'payment' in str(e).lower()):
                # If there's a cross-database error, temporarily disable problematic features
                # and try again with a simpler queryset
                original_list_display = self.list_display
                original_list_filter = self.list_filter
                original_search_fields = self.search_fields

                try:
                    # Simplify the admin interface temporarily
                    self.list_display = ['name', 'email', 'mobile_number', 'user_type', 'is_active', 'date_joined']
                    self.list_filter = ['user_type', 'is_active', 'date_joined']
                    self.search_fields = ['name', 'email', 'mobile_number']

                    return super().changelist_view(request, extra_context)
                finally:
                    # Restore original settings
                    self.list_display = original_list_display
                    self.list_filter = original_list_filter
                    self.search_fields = original_search_fields
            else:
                raise

    actions = ['unlock_accounts', 'verify_users', 'deactivate_users', 'activate_users']

    def unlock_accounts(self, request, queryset):
        """Unlock selected user accounts"""
        count = 0
        for user in queryset:
            if user.is_locked:
                user.unlock_account()
                count += 1

        self.message_user(request, f'{count} accounts unlocked successfully.')
    unlock_accounts.short_description = "Unlock selected accounts"

    def verify_users(self, request, queryset):
        """Verify selected users"""
        count = queryset.update(is_verified=True)
        self.message_user(request, f'{count} users verified successfully.')
    verify_users.short_description = "Verify selected users"

    def deactivate_users(self, request, queryset):
        """Deactivate selected users"""
        count = queryset.update(is_active=False)
        self.message_user(request, f'{count} users deactivated successfully.')
    deactivate_users.short_description = "Deactivate selected users"

    def activate_users(self, request, queryset):
        """Activate selected users"""
        count = queryset.update(is_active=True)
        self.message_user(request, f'{count} users activated successfully.')
    activate_users.short_description = "Activate selected users"

    def delete_model(self, request, obj):
        """
        Override delete to handle cross-database relationships properly.
        Manually delete related objects from other databases before deleting the user.
        """
        try:
            # Delete related objects from all microservice databases
            self._delete_user_cart_data(obj.id)
            self._delete_user_order_data(obj.id)
            self._delete_user_payment_data(obj.id)

            # Now delete the user from the default database
            super().delete_model(request, obj)

        except Exception as e:
            self.message_user(request, f'Error deleting user: {str(e)}', level='ERROR')

    def _delete_user_cart_data(self, user_id):
        """Helper method to delete user cart data from cart_db"""
        try:
            cart_db = connections['cart_db']
            with cart_db.cursor() as cursor:
                # Check if tables exist first
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'cart_cart'
                    );
                """)
                if cursor.fetchone()[0]:
                    cursor.execute("DELETE FROM cart_cartitem WHERE cart_id IN (SELECT id FROM cart_cart WHERE user_id = %s)", [user_id])
                    cursor.execute("DELETE FROM cart_cart WHERE user_id = %s", [user_id])
        except Exception as e:
            # Log the error but don't fail the user deletion
            print(f"Warning: Could not delete cart data for user {user_id}: {str(e)}")

    def _delete_user_order_data(self, user_id):
        """Helper method to delete user order data from orders_db"""
        try:
            orders_db = connections['orders_db']
            with orders_db.cursor() as cursor:
                # Check if tables exist first
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'orders_order'
                    );
                """)
                if cursor.fetchone()[0]:
                    # Delete order items first (foreign key constraint)
                    cursor.execute("DELETE FROM orders_orderitem WHERE order_id IN (SELECT id FROM orders_order WHERE customer_id = %s OR assigned_provider_id = %s)", [user_id, user_id])
                    # Delete orders where user is customer or assigned provider
                    cursor.execute("DELETE FROM orders_order WHERE customer_id = %s OR assigned_provider_id = %s", [user_id, user_id])
        except Exception as e:
            # Log the error but don't fail the user deletion
            print(f"Warning: Could not delete order data for user {user_id}: {str(e)}")

    def _delete_user_payment_data(self, user_id):
        """Helper method to delete user payment data from payments_db"""
        try:
            payments_db = connections['payments_db']
            with payments_db.cursor() as cursor:
                # Check if tables exist first
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'payments_paymenttransaction'
                    );
                """)
                if cursor.fetchone()[0]:
                    cursor.execute("DELETE FROM payments_paymenttransaction WHERE user_id = %s", [user_id])
        except Exception as e:
            # Log the error but don't fail the user deletion
            print(f"Warning: Could not delete payment data for user {user_id}: {str(e)}")

    def delete_queryset(self, request, queryset):
        """
        Override bulk delete to handle cross-database relationships properly.
        """
        try:
            user_ids = list(queryset.values_list('id', flat=True))

            if user_ids:
                # Delete related objects from all microservice databases for all users
                for user_id in user_ids:
                    self._delete_user_cart_data(user_id)
                    self._delete_user_order_data(user_id)
                    self._delete_user_payment_data(user_id)

            # Now delete the users from the default database
            super().delete_queryset(request, queryset)

        except Exception as e:
            self.message_user(request, f'Error deleting users: {str(e)}', level='ERROR')


@admin.register(Address)
class AddressAdmin(admin.ModelAdmin):
    """
    Admin interface for Address model
    """
    list_display = [
        'user', 'address_type', 'city', 'state', 'is_default', 'created_at'
    ]
    list_filter = ['address_type', 'is_default', 'state', 'created_at']
    search_fields = ['user__name', 'user__email', 'user__mobile_number', 'city', 'state']
    ordering = ['-created_at']

    fieldsets = (
        (None, {
            'fields': ('user', 'address_type', 'is_default')
        }),
        ('Address Details', {
            'fields': ('street', 'city', 'state', 'zip_code', 'landmark')
        }),
    )

    readonly_fields = ['created_at', 'updated_at']


@admin.register(FailedLoginAttempt)
class FailedLoginAttemptAdmin(admin.ModelAdmin):
    """
    Admin interface for Failed Login Attempts
    """
    list_display = [
        'get_identifier', 'ip_address', 'timestamp', 'user_link'
    ]
    list_filter = ['timestamp']
    search_fields = ['user__name', 'user__email', 'mobile_number', 'email', 'ip_address']
    ordering = ['-timestamp']
    readonly_fields = ['user', 'mobile_number', 'email', 'ip_address', 'timestamp']

    def get_identifier(self, obj):
        """Get user identifier (email or mobile)"""
        if obj.email:
            return obj.email
        elif obj.mobile_number:
            return obj.mobile_number
        else:
            return 'Unknown'
    get_identifier.short_description = 'Identifier'

    def user_link(self, obj):
        """Create link to user admin page"""
        if obj.user:
            url = reverse('admin:authentication_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.name)
        return 'No User'
    user_link.short_description = 'User'

    def has_add_permission(self, request):
        """Disable adding failed attempts manually"""
        return False

    def has_change_permission(self, request, obj=None):
        """Disable editing failed attempts"""
        return False


# Customize admin site
admin.site.site_header = 'Home Services Authentication Admin'
admin.site.site_title = 'Home Services Admin'
admin.site.index_title = 'Authentication Service Administration'
